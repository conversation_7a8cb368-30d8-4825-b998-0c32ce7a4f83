import type { RouteRecordRaw } from "vue-router"
import { createRouter } from "vue-router"
import { routerConfig } from "@/router/config"
import { registerNavigationGuard } from "@/router/guard"
import { flatMultiLevelRoutes } from "./helper"

const Layouts = () => import("@/layouts/index.vue")
const SimpleLayout = () => import("@/layouts/SimpleLayout.vue")

/**
 * @name 常驻路由
 * @description 除了 redirect/403/404/login 等隐藏页面，其他页面建议设置唯一的 Name 属性
 */
export const constantRoutes: RouteRecordRaw[] = [
  {
    path: "/redirect",
    component: Layouts,
    meta: {
      hidden: true
    },
    children: [
      {
        path: ":path(.*)",
        component: () => import("@/pages/redirect/index.vue")
      }
    ]
  },
  {
    path: "/403",
    component: () => import("@/pages/error/403.vue"),
    meta: {
      hidden: true
    }
  },
  {
    path: "/404",
    component: () => import("@/pages/error/404.vue"),
    meta: {
      hidden: true
    },
    alias: "/:pathMatch(.*)*"
  },
  {
    path: "/login",
    component: () => import("@/pages/login/index.vue"),
    meta: {
      hidden: true
    }
  },
  {
    path: "/",
    component: Layouts,
    redirect: "/dashboard",
    children: [
      {
        path: "dashboard",
        component: () => import("@/pages/dashboard/index.vue"),
        name: "Dashboard",
        meta: {
          title: "首页",
          svgIcon: "dashboard",
          affix: true
        }
      }
    ]
  },
  {
    path: "/system",
    component: Layouts,
    redirect: "/system/role",
    name: "System",
    meta: {
      title: "系统管理",
      elIcon: "Setting",
      alwaysShow: true
    },
    children: [
      {
        path: "role",
        component: () => import("@/pages/role/management/index.vue"),
        name: "RoleManagement",
        meta: {
          title: "角色管理",
          elIcon: "User",
          keepAlive: true
        }
      },
      {
        path: "user",
        component: () => import("@/pages/user/management/index.vue"),
        name: "UserManagement",
        meta: {
          title: "用户管理",
          elIcon: "Avatar",
          keepAlive: true
        }
      },
      {
        path: "permission",
        component: () => import("@/pages/permission/management/index.vue"),
        name: "PermissionManagement",
        meta: {
          title: "权限管理",
          elIcon: "Lock",
          keepAlive: true
        }
      }
    ]
  },
  {
    path: "/patient",
    component: Layouts,
    redirect: "/patient/management",
    name: "Patient",
    meta: {
      title: "患者管理"
    },
    children: [
      {
        path: "management",
        component: () => import("@/pages/patient/management/index.vue"),
        name: "PatientManagement",
        meta: {
          title: "患者管理",
          svgIcon: "huanzhe",
          keepAlive: true
        }
      },
      {
        path: "detail/:id",
        component: () => import("@/pages/patient/detail/index.vue"),
        name: "PatientDetail",
        meta: {
          title: "患者详情",
          hidden: true,
          keepAlive: false
        }
      },
      {
        path: "image-analysis/:patientId",
        component: () => import("@/pages/patient/image-analysis/index.vue"),
        name: "ImageAnalysis",
        meta: {
          title: "图像分析",
          hidden: true,
          keepAlive: false
        }
      },
      {
        path: "report-style/:patientId",
        component: () => import("@/pages/patient/report-style/index.vue"),
        name: "ReportStyle",
        meta: {
          title: "报告样式",
          hidden: true,
          keepAlive: false
        }
      }
    ]
  },
  {
    path: "/report",
    component: Layouts,
    redirect: "/report/management",
    name: "Report",
    meta: {
      title: "报告管理"
    },
    children: [
      {
        path: "management",
        component: () => import("@/pages/report/management/index.vue"),
        name: "ReportManagement",
        meta: {
          title: "报告管理",
          elIcon: "Document",
          keepAlive: true
        }
      }
    ]
  },
  {
    path: "/image-analysis",
    component: SimpleLayout,
    meta: {
      hidden: true
    },
    children: [
      {
        path: ":imageData?",
        component: () => import("@/pages/patient/detail/components/ImageAnalysis.vue"),
        name: "ImageAnalysisStandalone",
        meta: {
          title: "图像分析",
          hidden: true,
          keepAlive: false
        }
      }
    ]
  },
  {
    path: "/imageview",
    component: () => import("@/pages/imageview/index.vue"),
    name: "ImageViewer",
    meta: {
      title: "影像查看",
      hidden: true,
      keepAlive: false
    }
  },
  {
    path: "/profile",
    component: Layouts,
    name: "Profile",
    meta: {
      title: "个人中心",
      hidden: true
    },
    children: [
      {
        path: "",
        component: () => import("@/pages/profile/index.vue"),
        name: "ProfileIndex",
        meta: {
          title: "个人中心",
          keepAlive: true
        }
      }
    ]
  },
  {
    path: "/demo",
    component: Layouts,
    redirect: "/demo/element-plus",
    name: "Demo",
    meta: {
      title: "示例集合",
      elIcon: "DataBoard"
    },
    children: [
      {
        path: "element-plus",
        component: () => import("@/pages/demo/element-plus/index.vue"),
        name: "ElementPlus",
        meta: {
          title: "Element Plus",
          keepAlive: true
        }
      },
      {
        path: "vxe-table",
        component: () => import("@/pages/demo/vxe-table/index.vue"),
        name: "VxeTable",
        meta: {
          title: "Vxe Table",
          keepAlive: true
        }
      }
    ]
  }
]

/**
 * @name 动态路由
 * @description 用来放置有权限 (Roles 属性) 的路由
 * @description 必须带有唯一的 Name 属性
 */
export const dynamicRoutes: RouteRecordRaw[] = []

/** 路由实例 */
export const router = createRouter({
  history: routerConfig.history,
  routes: routerConfig.thirdLevelRouteCache ? flatMultiLevelRoutes(constantRoutes) : constantRoutes
})

/** 重置路由 */
export function resetRouter() {
  try {
    // 注意：所有动态路由路由必须带有 Name 属性，否则可能会不能完全重置干净
    router.getRoutes().forEach((route) => {
      const { name, meta } = route
      if (name && meta.roles?.length) {
        router.hasRoute(name) && router.removeRoute(name)
      }
    })
  } catch {
    // 强制刷新浏览器也行，只是交互体验不是很好
    location.reload()
  }
}

// 注册路由导航守卫
registerNavigationGuard(router)
